import React, { RefObject } from 'react';
import { FlatListProps } from 'react-native';
import {
  CModal,
  CModalHandle,
  ConditionView,
  FlatList,
  IUserLocation,
  LocationItem,
  Maybe,
} from '@btaskee/design-system';

import { useAppNavigation, useI18n } from '@hooks';

import { LocationEmpty } from '../location-empty';
import { styles } from './styles';

export interface ChooseAddressModalProps {
  modalRef?: RefObject<CModalHandle>;
  locations?: Maybe<IUserLocation[]>;
  currentAddress?: Pick<IUserLocation, 'lat' | 'lng'>;
  onAddNewAddress?: () => void;
  onChooseAddress?: (address: IUserLocation) => void;
  testID?: string;
}

export const ChooseAddressModal: React.FC<ChooseAddressModalProps> = ({
  modalRef,
  locations,
  onAddNewAddress,
  onChooseAddress,
  testID = 'choose-address-modal',
}) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();

  const _onAddNewAddress = () => {
    onCloseModal();
    onAddNewAddress?.();
  };

  const onEditAddress = (item: IUserLocation) => () => {
    onCloseModal();
    // Note: Navigation route needs to be defined in the navigation system
    // navigation.navigate('EditLocation', { location: item });
  };

  const onPressItem: ChooseAddressModalProps['onChooseAddress'] = (item) => {
    onCloseModal();
    onChooseAddress?.(item);
  };

  const onCloseModal = () => {
    modalRef?.current?.close();
  };

  const keyExtractor: FlatListProps<IUserLocation>['keyExtractor'] = (
    item,
    index,
  ) => `${item?.lat}-${item?.lng}-${index}`;

  const _renderItem: FlatListProps<IUserLocation>['renderItem'] = ({
    item,
    index,
  }) => {
    return (
      <LocationItem
        testIDs={{
          item: `location-item-${index}`,
          editBtn: `edit-location-${index}`,
        }}
        shortAddress={item?.shortAddress}
        address={item?.address}
        homeType={item?.homeType}
        containerStyle={styles.containerItem}
        isHideIcon
        isShowUpdate={Boolean(item._id)}
        onPress={() => onPressItem(item)}
        onPressUpdate={onEditAddress(item)}
      />
    );
  };

  const _renderEmptyComponent = () => (
    <LocationEmpty
      testID="choose-address-empty"
      label={t('LOCATION_EMPTY')}
    />
  );

  return (
    <CModal
      ref={modalRef}
      title={t('CHOOSE_ADDRESS')}
      contentContainerStyle={styles.containerModal}
      actions={[
        {
          text: t('ADD_ADDRESS'),
          onPress: _onAddNewAddress,
        },
      ]}
      testID={testID}
    >
      <ConditionView
        condition={!!locations?.length}
        viewTrue={
          <FlatList
            data={locations || []}
            keyExtractor={keyExtractor}
            renderItem={_renderItem}
            showsVerticalScrollIndicator={false}
            testID="scroll-choose-address"
            ListEmptyComponent={_renderEmptyComponent}
          />
        }
        viewFalse={_renderEmptyComponent()}
      />
    </CModal>
  );
};
