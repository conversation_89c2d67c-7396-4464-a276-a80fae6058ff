import React, { useMemo } from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  HomeMovingProgressPostTaskType,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { Section } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { styles } from './styles';

type ChooseHomeTypeProps = {
  step: HomeMovingProgressPostTaskType;
};

export const ChooseHomeType = ({ step }: ChooseHomeTypeProps) => {
  const { t } = useI18n();
  const {
    getDetailSettingHomeMoving,
    getDataPostTaskMoving,
    onChooseHomeType,
    getImageHomeType,
    getContentByHomeType,
  } = usePostTaskHomeMoving();

  const { currentStep } = usePostTaskStore();

  const dataPostTask = useMemo(() => {
    return getDataPostTaskMoving(step);
  }, [getDataPostTaskMoving, step]);

  const currentHomeDetail = useMemo(() => {
    return dataPostTask.homeDetail;
  }, [dataPostTask.homeDetail]);

  const currentCity = useMemo(() => {
    return currentHomeDetail.addressDetail?.taskPlace?.city;
  }, [currentHomeDetail.addressDetail?.taskPlace?.city]);

  const homeTypesSetting = useMemo(() => {
    return getDetailSettingHomeMoving(currentCity)?.homeTypes || [];
  }, [getDetailSettingHomeMoving, currentCity]);

  const isFocusStep = useMemo(() => {
    return currentStep === step;
  }, [currentStep, step]);

  if (!isFocusStep || !currentHomeDetail.addressDetail?.address) {
    return null;
  }

  return (
    <Section
      title={t('MOVING.CHOOSE_HOME_TYPE')}
      margin={{ top: Spacing.SPACE_24 }}
    >
      <BlockView margin={{ top: Spacing.SPACE_16 }}>
        {homeTypesSetting.map((homeType, index) => {
          const isSelected = currentHomeDetail?.homeType?.name === homeType.name;
          const imageSource = getImageHomeType(homeType.name);
          const content = getContentByHomeType(homeType.name);

          return (
            <TouchableOpacity
              key={`home-type-${index}`}
              testID={`homeType${homeType.name}Btn`}
              style={[
                styles.homeTypeItem,
                isSelected && styles.homeTypeItemSelected,
              ]}
              onPress={() => onChooseHomeType({ step, homeType })}
            >
              <BlockView row center>
                <BlockView style={styles.imageContainer}>
                  <IconImage
                    source={imageSource}
                    size={40}
                    color={isSelected ? ColorsV2.orange500 : ColorsV2.neutral600}
                  />
                </BlockView>
                <BlockView flex margin={{ left: Spacing.SPACE_12 }}>
                  <CText
                    size={FontSizes.SIZE_16}
                    color={isSelected ? ColorsV2.orange500 : ColorsV2.neutral800}
                    bold={isSelected}
                  >
                    {content.title}
                  </CText>
                  <ConditionView
                    condition={Boolean(content.subTitle)}
                    viewTrue={
                      <CText
                        size={FontSizes.SIZE_14}
                        color={ColorsV2.neutral600}
                        margin={{ top: Spacing.SPACE_04 }}
                      >
                        {content.subTitle}
                      </CText>
                    }
                  />
                </BlockView>
                <ConditionView
                  condition={isSelected}
                  viewTrue={
                    <IconImage
                      name="check-circle"
                      size={24}
                      color={ColorsV2.orange500}
                    />
                  }
                />
              </BlockView>
            </TouchableOpacity>
          );
        })}
      </BlockView>
    </Section>
  );
};
