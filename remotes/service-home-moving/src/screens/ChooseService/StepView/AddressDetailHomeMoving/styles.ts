import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
  },
  contentContainer: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingBottom: Spacing.SPACE_64,
  },
  addressSection: {
    marginTop: Spacing.SPACE_08,
  },
  addAddressButton: {
    backgroundColor: ColorsV2.neutralWhite,
    borderColor: ColorsV2.orange500,
    borderWidth: 1,
    marginTop: Spacing.SPACE_12,
  },
  addAddressButtonTitle: {
    color: ColorsV2.orange500,
    fontWeight: '400',
  },
  processButton: {
    marginTop: Spacing.SPACE_24,
  },
});
