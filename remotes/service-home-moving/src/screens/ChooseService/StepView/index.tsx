import React, { useEffect, useMemo, useRef } from 'react';
import {
  BlockView,
  BottomView,
  ColorsV2,
  ConditionView,
  CText,
  HomeMovingProgressPostTaskType,
  IHomeDetail,
  IUserLocation,
  KeyboardAware,
  LocationItem,
  Maybe,
  PrimaryButton,
  ProcessButton,
  SizedBox,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { debounce } from 'lodash-es';

import {
  ChooseAddressModal,
  ConfirmChangeHomeTypeContent,
  LocationEmpty,
} from '@components';
import { useAppNavigation, useI18n, usePostTaskHomeMoving } from '@hooks';

import { ChooseHomeType } from './components/ChooseHomeType';
import { ChooseOptionAddress } from './components/ChooseOptionAddress';
import { styles } from './styles';

export type AddressDetailHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
  title?: string;
  location?: IUserLocation;
  homeDetail?: IHomeDetail;
  blackListLocation?: Maybe<string>[];
  titleButtonNext?: string;
  labelOptionClean?: string;
  onChooseLocation?: (location?: Maybe<IUserLocation>) => void;
};

export const AddressDetailHomeMoving = ({
  step,
}: AddressDetailHomeMovingProps) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const {
    getDetailSettingHomeType,
    getContentByStep,
    getDataPostTaskMoving,
    onChangeLocation,
    getBlackListLocation,
    onNextStep,
    checkIsSupportCity,
    onAddLocation,
    getLocations,
    getIsShowAddIsInBuilding,
  } = usePostTaskHomeMoving();

  // Use Zustand store instead of Redux
  const {
    currentStep,
    isInBuilding,
    oldHomeDetail,
    temptStairsTransportStep1,
    setIsInBuilding,
    setHomeDetail,
    setTemptStairsTransportStep1,
    resetState,
  } = usePostTaskStore();

  const modalChooseAddressRef = useRef<any>(null);

  const dataPostTask = useMemo(() => {
    return getDataPostTaskMoving(step);
  }, [getDataPostTaskMoving, step]);

  const contentPostTask = useMemo(() => {
    return getContentByStep(step);
  }, [getContentByStep, step]);

  const currentHomeDetail = useMemo(() => {
    return dataPostTask.homeDetail;
  }, [dataPostTask.homeDetail]);

  const locations = useMemo(() => {
    return getLocations();
  }, [getLocations]);

  const blackListLocation = useMemo(() => {
    return getBlackListLocation(step);
  }, [getBlackListLocation, step]);

  const isDisableButton = useMemo(() => {
    const isSupportCity = checkIsSupportCity(
      currentHomeDetail.addressDetail?.taskPlace?.city,
    );
    const nameHomeTypeMoving = currentHomeDetail.homeType?.name;

    if (!isSupportCity || !nameHomeTypeMoving) {
      return true;
    }

    if (step === HomeMovingProgressPostTaskType.Step1) {
      return !currentHomeDetail?.homeType?.type?.name;
    }

    return false;
  }, [checkIsSupportCity, currentHomeDetail, step]);

  const isFocusStep2 = useMemo(() => {
    return (
      currentStep === HomeMovingProgressPostTaskType.Step2 &&
      step === HomeMovingProgressPostTaskType.Step2
    );
  }, [currentStep, step]);

  const isStep2 = useMemo(() => {
    return step === HomeMovingProgressPostTaskType.Step2;
  }, [step]);

  const contentContainerStyle = useMemo(() => {
    return {
      paddingBottom: Spacing.SPACE_24,
    };
  }, []);

  const isShowAddIsInBuilding = useMemo(() => {
    return getIsShowAddIsInBuilding();
  }, [getIsShowAddIsInBuilding]);

  const onAddNewAddressDebounce = useRef(debounce(onAddLocation, 500)).current;

  useEffect(() => {
    if (isFocusStep2 && !currentHomeDetail.addressDetail?.address) {
      onAddNewAddressDebounce({
        title: contentPostTask.title,
        step,
        isShowAddIsInBuilding,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentHomeDetail.addressDetail?.address,
    currentStep,
    isFocusStep2,
    step,
  ]);

  const _onChangeLocation = (location?: Maybe<IUserLocation>) => {
    onChangeLocation?.({ step, location });
  };

  const _onAddNewAddress = () => {
    onAddLocation({
      title: contentPostTask.title,
      step,
      isShowAddIsInBuilding,
    });
  };

  const onChangeAddress = () => {
    if (isInBuilding) {
      showConfirmChangeHomeType();
      return;
    }

    // Nếu là step 1 thì mở modal list location
    if (step === HomeMovingProgressPostTaskType.Step1 && locations.length) {
      modalChooseAddressRef?.current?.open &&
        modalChooseAddressRef.current?.open();
      return;
    }
    _onAddNewAddress();
  };

  const showConfirmChangeHomeType = () => {
    // Use design system Alert instead of AlertHolder
    // This would need to be implemented with the design system's alert component
    // For now, we'll use a simple confirm
    const confirmed = window.confirm(t('MOVING.CONFIRM_CHANGE'));
    if (confirmed) {
      // Nếu ở step "Chọn nơi đi" thì reset lại state của post task home moving
      if (step === HomeMovingProgressPostTaskType.Step1) {
        resetState();
        return;
      }

      // Trường hợp nếu ở step chọn nơi đến

      // Nếu biến tempt option "vận chuyển thang bộ" có giá trị thì set lại cho nơi đi
      if (temptStairsTransportStep1) {
        setTemptStairsTransportStep1(null);

        setHomeDetail({
          step: HomeMovingProgressPostTaskType.Step1,
          homeDetail: {
            ...oldHomeDetail,
            options: [
              temptStairsTransportStep1,
              ...(oldHomeDetail?.options || []),
            ],
          },
        });
      }

      // Set option chuyển cùng tòa nhà = false
      setIsInBuilding(false);

      // Clear newHomeDetail
      setHomeDetail({
        step: HomeMovingProgressPostTaskType.Step2,
        homeDetail: {},
      });
    }
  };

  const onPressChangeAddress = () => {
    onChangeAddress();
  };

  const onNext = () => {
    onNextStep(step);
  };

  const goToMovingProgress = () => {
    // This would need to be implemented based on the navigation structure
    // navigation.navigate('MovingProgress');
  };

  return (
    <>
      <BlockView style={styles.container}>
        <KeyboardAware
          testID="AddressDetailHomeMovingScrollView"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={contentContainerStyle}
        >
          <BlockView margin={{ top: Spacing.SPACE_08 }}>
            <ConditionView
              condition={Boolean(currentHomeDetail.addressDetail)}
              viewTrue={
                <LocationItem
                  testIDs={{
                    editBtn: 'editBtnHomeDetail',
                  }}
                  shortAddress={currentHomeDetail.addressDetail?.shortAddress}
                  address={currentHomeDetail.addressDetail?.address}
                  onPressUpdate={onPressChangeAddress}
                />
              }
              viewFalse={
                <LocationEmpty
                  testID="locationEmptyHomeMoving"
                  label={contentPostTask.labelEmpty}
                  onPress={_onAddNewAddress}
                />
              }
            />
          </BlockView>
          <ChooseHomeType step={step} />
          <ChooseOptionAddress step={step} />
          <SizedBox height={30} />
          <ProcessButton
            label={t('MOVING.MOVING_PROCESS')}
            onPress={goToMovingProgress}
          />
        </KeyboardAware>
      </BlockView>
      <ConditionView
        condition={!isStep2}
        viewTrue={
          <BottomView>
            <PrimaryButton
              testID="btnNextMoving"
              title={contentPostTask.labelButton}
              onPress={onNext}
              disabled={isDisableButton}
            />
          </BottomView>
        }
      />
      <ChooseAddressModal
        modalRef={modalChooseAddressRef}
        locations={locations}
        onChooseAddress={_onChangeLocation}
        onAddNewAddress={_onAddNewAddress}
      />
    </>
  );
};
